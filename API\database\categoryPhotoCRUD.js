const db = require('../db');

/**
 * Category Photo CRUD Operations
 * Handles all category photo-related database operations
 * Designed for single image per category (unlike products which support multiple)
 */

// Get photo for a category
const getPhotoByCategoryId = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM category_photos WHERE category_id = ?', [req.params.categoryId]);
    res.json({ success: true, data: rows.length > 0 ? rows[0] : null });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Add or update photo for category (since categories have single image, this replaces existing)
const createOrUpdatePhoto = async (req, res) => {
  try {
    const { photo_url } = req.body;
    const categoryId = req.params.categoryId;

    // Check if category exists
    const [categoryCheck] = await db.execute('SELECT id FROM categories WHERE id = ?', [categoryId]);
    if (categoryCheck.length === 0) {
      return res.status(404).json({ success: false, message: 'Category not found' });
    }

    // Check if photo already exists for this category
    const [existingPhoto] = await db.execute('SELECT id FROM category_photos WHERE category_id = ?', [categoryId]);
    
    if (existingPhoto.length > 0) {
      // Update existing photo
      const [result] = await db.execute(
        'UPDATE category_photos SET photo_url = ? WHERE category_id = ?',
        [photo_url, categoryId]
      );
      res.json({ success: true, id: existingPhoto[0].id, message: 'Category photo updated successfully' });
    } else {
      // Create new photo
      const [result] = await db.execute(
        'INSERT INTO category_photos (category_id, photo_url, created_at) VALUES (?, ?, NOW())',
        [categoryId, photo_url]
      );
      res.status(201).json({ success: true, id: result.insertId, message: 'Category photo added successfully' });
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Delete photo for category
const deletePhoto = async (req, res) => {
  try {
    const categoryId = req.params.categoryId;
    const [result] = await db.execute('DELETE FROM category_photos WHERE category_id = ?', [categoryId]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Category photo not found' });
    }
    
    res.json({ success: true, message: 'Category photo deleted successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Delete photo by photo ID (alternative endpoint)
const deletePhotoById = async (req, res) => {
  try {
    const [result] = await db.execute('DELETE FROM category_photos WHERE id = ?', [req.params.id]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Category photo not found' });
    }
    
    res.json({ success: true, message: 'Category photo deleted successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

module.exports = {
  getPhotoByCategoryId,
  createOrUpdatePhoto,
  deletePhoto,
  deletePhotoById
};
