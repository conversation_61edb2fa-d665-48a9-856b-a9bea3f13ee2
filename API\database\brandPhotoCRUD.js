const db = require('../db');

/**
 * Brand Photo CRUD Operations
 * Handles all brand photo-related database operations
 * Designed for single image per brand (unlike products which support multiple)
 */

// Get photo for a brand
const getPhotoByBrandId = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM brand_photos WHERE brand_id = ?', [req.params.brandId]);
    res.json({ success: true, data: rows.length > 0 ? rows[0] : null });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Add or update photo for brand (since brands have single image, this replaces existing)
const createOrUpdatePhoto = async (req, res) => {
  try {
    const { photo_url } = req.body;
    const brandId = req.params.brandId;

    // Check if brand exists
    const [brandCheck] = await db.execute('SELECT id FROM brands WHERE id = ?', [brandId]);
    if (brandCheck.length === 0) {
      return res.status(404).json({ success: false, message: 'Brand not found' });
    }

    // Check if photo already exists for this brand
    const [existingPhoto] = await db.execute('SELECT id FROM brand_photos WHERE brand_id = ?', [brandId]);
    
    if (existingPhoto.length > 0) {
      // Update existing photo
      const [result] = await db.execute(
        'UPDATE brand_photos SET photo_url = ? WHERE brand_id = ?',
        [photo_url, brandId]
      );
      res.json({ success: true, id: existingPhoto[0].id, message: 'Brand photo updated successfully' });
    } else {
      // Create new photo
      const [result] = await db.execute(
        'INSERT INTO brand_photos (brand_id, photo_url, created_at) VALUES (?, ?, NOW())',
        [brandId, photo_url]
      );
      res.status(201).json({ success: true, id: result.insertId, message: 'Brand photo added successfully' });
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Delete photo for brand
const deletePhoto = async (req, res) => {
  try {
    const brandId = req.params.brandId;
    const [result] = await db.execute('DELETE FROM brand_photos WHERE brand_id = ?', [brandId]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Brand photo not found' });
    }
    
    res.json({ success: true, message: 'Brand photo deleted successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Delete photo by photo ID (alternative endpoint)
const deletePhotoById = async (req, res) => {
  try {
    const [result] = await db.execute('DELETE FROM brand_photos WHERE id = ?', [req.params.id]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Brand photo not found' });
    }
    
    res.json({ success: true, message: 'Brand photo deleted successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

module.exports = {
  getPhotoByBrandId,
  createOrUpdatePhoto,
  deletePhoto,
  deletePhotoById
};
