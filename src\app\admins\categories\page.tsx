'use client';

import React, { useState } from 'react';
import AdminLayout from '@/components/AdminLayout';
import { ProtectedRoute } from '@/contexts/AuthContext';
import { useListData, useFormSubmit, useModal, useForm } from '@/lib/hooks';
import { categoryAPI, categoryPhotoAPI } from '@/lib/api';
import { formatDate, truncateText, getInputClassName } from '@/lib/utils';
import ImageUpload from '@/components/ImageUpload';

interface Category {
  id: number;
  name: string;
  description?: string;
  category_photo?: string;
  created_at: string;
  updated_at: string;
}

interface CategoryForm {
  name: string;
  description: string;
  category_photo: string;
}

function CategoryModal({ isOpen, onClose, category, onSuccess }: {
  isOpen: boolean;
  onClose: () => void;
  category?: Category;
  onSuccess: () => void;
}) {
  const isEdit = !!category;
  const { loading, error, success, submit, reset } = useFormSubmit();
  const [uploadError, setUploadError] = useState<string | null>(null);
  
  const {
    values,
    errors,
    handleChange,
    setError,
    clearErrors,
    reset: resetForm,
    setValue
  } = useForm<CategoryForm>({
    name: category?.name || '',
    description: category?.description || '',
    category_photo: category?.category_photo || ''
  });

  const validateForm = (): boolean => {
    clearErrors();
    let isValid = true;

    if (!values.name.trim()) {
      setError('name', 'Category name is required');
      isValid = false;
    }

    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    const success = await submit(async () => {
      let categoryId: number;

      // Create or update category (without photo data)
      const categoryData = { name: values.name, description: values.description };
      if (isEdit) {
        await categoryAPI.updateCategory(category.id, categoryData);
        categoryId = category.id;
      } else {
        const result = await categoryAPI.createCategory(categoryData);
        categoryId = result.data.id;
      }

      // Handle photo separately if provided
      if (values.category_photo) {
        await categoryPhotoAPI.createOrUpdateCategoryPhoto(categoryId, { photo_url: values.category_photo });
      }

      return { success: true };
    });

    if (success) {
      onSuccess();
      onClose();
      resetForm();
      reset();
    }
  };

  const handleClose = () => {
    onClose();
    resetForm();
    reset();
    setUploadError(null);
  };

  const handleImageUploaded = (imageUrl: string) => {
    setValue('category_photo', imageUrl);
    setUploadError(null);
  };

  const handleImageError = (error: string) => {
    setUploadError(error);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-6 border w-full max-w-5xl shadow-lg rounded-md bg-white min-h-[90vh]">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-900">
              {isEdit ? 'Edit Category' : 'Create New Category'}
            </h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Global Messages */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {success && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex">
                <svg className="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <p className="text-sm text-green-800">{success}</p>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {/* Two Column Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

              {/* Column 1: Image Preview & Upload */}
              <div>
                <div className="bg-gray-50 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Category Image</h4>
                  {uploadError && (
                    <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
                      <p className="text-sm text-red-800">{uploadError}</p>
                    </div>
                  )}

                  {/* Image Preview */}
                  {values.category_photo && (
                    <div className="mb-4">
                      <div className="relative">
                        <img
                          src={values.category_photo}
                          alt="Category preview"
                          className="w-full h-48 object-cover rounded-lg border border-gray-300"
                        />
                        <button
                          type="button"
                          onClick={() => setValue('category_photo', '')}
                          className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )}

                  <ImageUpload
                    onImageUploaded={handleImageUploaded}
                    onError={handleImageError}
                    uploadType="category"
                  />
                </div>
              </div>

              {/* Column 2: Category Form */}
              <div>
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-6">Category Details</h4>
                  <div className="space-y-6">

                    {/* Category Name */}
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        Category Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={values.name}
                        onChange={handleChange}
                        className={getInputClassName(!!errors.name)}
                        placeholder="Enter category name"
                      />
                      {errors.name && (
                        <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                      )}
                    </div>

                    {/* Description */}
                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                        Description
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        rows={4}
                        value={values.description}
                        onChange={handleChange}
                        className={getInputClassName()}
                        placeholder="Enter category description (optional)"
                      />
                    </div>

                  </div>
                </div>
              </div>

            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-8 border-t border-gray-200 mt-8">
              <button
                type="button"
                onClick={handleClose}
                className="px-6 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120] transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className={`px-6 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition-colors ${
                  loading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]'
                }`}
              >
                {loading ? 'Saving...' : (isEdit ? 'Update Category' : 'Create Category')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function CategoriesContent() {
  const { items: categories, loading, error, refetch, addItem, updateItem, removeItem } = useListData<Category>(
    () => categoryAPI.getAllCategories()
  );
  const { isOpen, data: selectedCategory, open, close } = useModal();
  const [deleteLoading, setDeleteLoading] = useState<number | null>(null);

  const handleCreate = () => {
    open();
  };

  const handleEdit = (category: Category) => {
    open(category);
  };

  const handleDelete = async (category: Category) => {
    if (!confirm(`Are you sure you want to delete "${category.name}"?`)) return;

    setDeleteLoading(category.id);
    try {
      const response = await categoryAPI.deleteCategory(category.id);
      if (response.success) {
        removeItem(category.id);
      } else {
        alert(response.error || 'Failed to delete category');
      }
    } catch (error: any) {
      alert(error.response?.data?.error || 'Failed to delete category');
    } finally {
      setDeleteLoading(null);
    }
  };

  const handleModalSuccess = () => {
    refetch();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E6B120]"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Categories</h1>
          <p className="text-gray-600">Manage product categories</p>
        </div>
        <button
          onClick={handleCreate}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
        >
          <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Category
        </button>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Categories List */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        {categories.length === 0 ? (
          <div className="p-6 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No categories</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by creating a new category.</p>
            <div className="mt-6">
              <button
                onClick={handleCreate}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Category
              </button>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {categories.map((category) => (
              <div key={category.id} className="p-6 flex items-center justify-between">
                <div className="flex items-center space-x-4 flex-1">
                  {category.category_photo && (
                    <img
                      src={category.category_photo}
                      alt={category.name}
                      className="w-16 h-16 object-cover rounded-lg border border-gray-200"
                    />
                  )}
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900">{category.name}</h3>
                  {category.description && (
                    <p className="text-sm text-gray-500 mt-1">
                      {truncateText(category.description, 100)}
                    </p>
                  )}
                    <p className="text-xs text-gray-400 mt-2">
                      Created: {formatDate(category.created_at)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(category)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(category)}
                    disabled={deleteLoading === category.id}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {deleteLoading === category.id ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal */}
      <CategoryModal
        isOpen={isOpen}
        onClose={close}
        category={selectedCategory}
        onSuccess={handleModalSuccess}
      />
    </div>
  );
}

export default function CategoriesPage() {
  return (
    <ProtectedRoute>
      <AdminLayout>
        <CategoriesContent />
      </AdminLayout>
    </ProtectedRoute>
  );
}
